{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function scrollToElement(elementId: string): void {\n  const element = document.getElementById(elementId);\n  if (element) {\n    element.scrollIntoView({\n      behavior: \"smooth\",\n      block: \"start\",\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YACrB,UAAU;YACV,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Menu, X, Home, User, Code, Briefcase, Youtube, Mail } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { href: \"/\", label: \"Home\", icon: Home },\n  { href: \"/about\", label: \"About\", icon: User },\n  { href: \"/skills\", label: \"Skills\", icon: Code },\n  { href: \"/projects\", label: \"Projects\", icon: Briefcase },\n  { href: \"/youtube\", label: \"YouTube\", icon: Youtube },\n  { href: \"/contact\", label: \"Contact\", icon: Mail },\n];\n\nexport default function Navigation() {\n  const pathname = usePathname();\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const toggleMenu = () => setIsOpen(!isOpen);\n\n  return (\n    <>\n      {/* Desktop Navigation */}\n      <motion.nav\n        initial={{ y: -100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.5 }}\n        className={cn(\n          \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300\",\n          \"hidden md:block\",\n          scrolled ? \"glass-nav shadow-lg\" : \"glass-nav\"\n        )}\n      >\n        <div className=\"flex items-center space-x-1\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n            \n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  \"relative px-4 py-2 rounded-full transition-all duration-300\",\n                  \"flex items-center space-x-2 text-sm font-medium\",\n                  isActive\n                    ? \"text-white bg-white/20\"\n                    : \"text-white/80 hover:text-white hover:bg-white/10\"\n                )}\n              >\n                <Icon size={16} />\n                <span>{item.label}</span>\n                {isActive && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute inset-0 bg-white/20 rounded-full\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  />\n                )}\n              </Link>\n            );\n          })}\n        </div>\n      </motion.nav>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden\">\n        {/* Mobile Menu Button */}\n        <motion.button\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ delay: 0.2 }}\n          onClick={toggleMenu}\n          className=\"fixed top-4 right-4 z-50 glass-nav p-3\"\n        >\n          <AnimatePresence mode=\"wait\">\n            {isOpen ? (\n              <motion.div\n                key=\"close\"\n                initial={{ rotate: -90, opacity: 0 }}\n                animate={{ rotate: 0, opacity: 1 }}\n                exit={{ rotate: 90, opacity: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <X size={24} className=\"text-white\" />\n              </motion.div>\n            ) : (\n              <motion.div\n                key=\"menu\"\n                initial={{ rotate: 90, opacity: 0 }}\n                animate={{ rotate: 0, opacity: 1 }}\n                exit={{ rotate: -90, opacity: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Menu size={24} className=\"text-white\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </motion.button>\n\n        {/* Mobile Menu Overlay */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"fixed inset-0 z-40 bg-black/50 backdrop-blur-sm\"\n              onClick={toggleMenu}\n            />\n          )}\n        </AnimatePresence>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ x: \"100%\" }}\n              animate={{ x: 0 }}\n              exit={{ x: \"100%\" }}\n              transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n              className=\"fixed top-0 right-0 h-full w-80 z-50 glass p-6\"\n            >\n              <div className=\"flex flex-col space-y-4 mt-16\">\n                {navItems.map((item, index) => {\n                  const Icon = item.icon;\n                  const isActive = pathname === item.href;\n                  \n                  return (\n                    <motion.div\n                      key={item.href}\n                      initial={{ x: 50, opacity: 0 }}\n                      animate={{ x: 0, opacity: 1 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={toggleMenu}\n                        className={cn(\n                          \"flex items-center space-x-3 p-4 rounded-xl transition-all duration-300\",\n                          isActive\n                            ? \"bg-white/20 text-white\"\n                            : \"text-white/80 hover:text-white hover:bg-white/10\"\n                        )}\n                      >\n                        <Icon size={20} />\n                        <span className=\"font-medium\">{item.label}</span>\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;QAAQ,MAAM,sMAAA,CAAA,OAAI;IAAC;IACvC;QAAE,MAAM;QAAU,OAAO;QAAS,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC7C;QAAE,MAAM;QAAW,OAAO;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC/C;QAAE,MAAM;QAAa,OAAO;QAAY,MAAM,+MAAA,CAAA,YAAS;IAAC;IACxD;QAAE,MAAM;QAAY,OAAO;QAAW,MAAM,2MAAA,CAAA,UAAO;IAAC;IACpD;QAAE,MAAM;QAAY,OAAO;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;CAClD;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC/B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA,mBACA,WAAW,wBAAwB;0BAGrC,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,mDACA,WACI,2BACA;;8CAGN,6LAAC;oCAAK,MAAM;;;;;;8CACZ,6LAAC;8CAAM,KAAK,KAAK;;;;;;gCAChB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;oCACT,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;;;;;;;2BAjBzD,KAAK,IAAI;;;;;oBAsBpB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAI;wBACzB,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,QAAQ,CAAC;oCAAI,SAAS;gCAAE;gCACnC,SAAS;oCAAE,QAAQ;oCAAG,SAAS;gCAAE;gCACjC,MAAM;oCAAE,QAAQ;oCAAI,SAAS;gCAAE;gCAC/B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;oCAAI,WAAU;;;;;;+BANnB;;;;qDASN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,QAAQ;oCAAI,SAAS;gCAAE;gCAClC,SAAS;oCAAE,QAAQ;oCAAG,SAAS;gCAAE;gCACjC,MAAM;oCAAE,QAAQ,CAAC;oCAAI,SAAS;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;+BANtB;;;;;;;;;;;;;;;kCAaZ,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;4BACV,SAAS;;;;;;;;;;;kCAMf,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;4BAC1D,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM;oCACnB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,SAAS;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS;4CACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WACI,2BACA;;8DAGN,6LAAC;oDAAK,MAAM;;;;;;8DACZ,6LAAC;oDAAK,WAAU;8DAAe,KAAK,KAAK;;;;;;;;;;;;uCAhBtC,KAAK,IAAI;;;;;gCAoBpB;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GA5JwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}]}