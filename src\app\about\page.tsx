"use client";

import { motion } from "framer-motion";
import { GraduationCap, Heart, Target, Lightbulb, Coffee, Music } from "lucide-react";

const timelineData = [
  {
    year: "2024",
    title: "Senior Full-Stack Developer",
    company: "Tech Company",
    description: "Leading development of scalable web applications using modern technologies.",
  },
  {
    year: "2022",
    title: "Full-Stack Developer",
    company: "Startup Inc.",
    description: "Built and maintained multiple client projects using React, Node.js, and cloud services.",
  },
  {
    year: "2021",
    title: "Frontend Developer",
    company: "Digital Agency",
    description: "Specialized in creating responsive, user-friendly interfaces with React and TypeScript.",
  },
  {
    year: "2020",
    title: "Computer Science Graduate",
    company: "University Name",
    description: "Graduated with honors, focusing on software engineering and web technologies.",
  },
];

const values = [
  {
    icon: Target,
    title: "Purpose-Driven",
    description: "I believe in creating technology that makes a meaningful impact on people's lives.",
  },
  {
    icon: Lightbulb,
    title: "Innovation",
    description: "Always exploring new technologies and approaches to solve complex problems.",
  },
  {
    icon: Heart,
    title: "User-Centric",
    description: "Every line of code I write is with the end user's experience in mind.",
  },
];

const interests = [
  { icon: Coffee, label: "Coffee Enthusiast" },
  { icon: Music, label: "Music Production" },
  { icon: GraduationCap, label: "Continuous Learning" },
];

export default function About() {
  return (
    <div className="min-h-screen py-20 px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold text-white mb-6">
            About <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Me</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Passionate developer with a love for creating beautiful, functional, and user-centered digital experiences.
          </p>
        </motion.div>

        {/* Main Story */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="glass-card mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-6">My Story</h2>
          <div className="space-y-4 text-white/80 leading-relaxed">
            <p>
              My journey into the world of technology began during my university years when I discovered 
              the power of code to bring ideas to life. What started as curiosity quickly became a passion 
              that has driven my career for the past several years.
            </p>
            <p>
              I specialize in full-stack web development, with a particular love for creating seamless 
              user experiences using modern technologies like React, Next.js, and Node.js. I believe 
              that great software is not just about clean code, but about understanding and solving 
              real human problems.
            </p>
            <p>
              When I'm not coding, you'll find me exploring new technologies, contributing to open-source 
              projects, or sharing my knowledge through blog posts and community involvement. I'm always 
              excited to take on new challenges and collaborate with like-minded individuals.
            </p>
          </div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-8 text-center">My Journey</h2>
          <div className="space-y-8">
            {timelineData.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                className="relative"
              >
                <div className="glass-card">
                  <div className="flex items-start space-x-4">
                    <div className="glass-nav px-4 py-2 text-blue-300 font-bold text-lg">
                      {item.year}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-1">{item.title}</h3>
                      <p className="text-blue-300 font-medium mb-2">{item.company}</p>
                      <p className="text-white/80">{item.description}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Values */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-8 text-center">My Values</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                  className="glass-card text-center"
                >
                  <div className="glass-nav w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={24} className="text-blue-300" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">{value.title}</h3>
                  <p className="text-white/80">{value.description}</p>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Interests */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="glass-card text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-8">Beyond Code</h2>
          <div className="flex justify-center space-x-8">
            {interests.map((interest, index) => {
              const Icon = interest.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 1.4 + index * 0.1 }}
                  className="flex flex-col items-center space-y-2"
                >
                  <div className="glass-nav p-4 rounded-full">
                    <Icon size={24} className="text-purple-300" />
                  </div>
                  <span className="text-white/80 text-sm">{interest.label}</span>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
