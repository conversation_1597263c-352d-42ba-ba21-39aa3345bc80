"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ExternalLink, Github, Calendar, Tag } from "lucide-react";

const projects = [
  {
    id: 1,
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce solution with modern UI, payment integration, and admin dashboard.",
    longDescription: "Built with Next.js, TypeScript, and Stripe for payments. Features include user authentication, product management, shopping cart, order tracking, and comprehensive admin panel.",
    image: "/project1-placeholder.jpg",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Stripe", "PostgreSQL"],
    status: "Completed",
    date: "2024",
    githubUrl: "https://github.com/yourusername/ecommerce",
    liveUrl: "https://ecommerce-demo.vercel.app",
    category: "Web Development"
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates and team features.",
    longDescription: "React-based application with real-time collaboration features using Socket.io. Includes drag-and-drop functionality, team management, notifications, and progress tracking.",
    image: "/project2-placeholder.jpg",
    technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Express"],
    status: "Completed",
    date: "2023",
    githubUrl: "https://github.com/yourusername/taskmanager",
    liveUrl: "https://taskmanager-demo.vercel.app",
    category: "Web Development"
  },
  {
    id: 3,
    title: "Weather Mobile App",
    description: "Cross-platform mobile app providing detailed weather forecasts and location-based alerts.",
    longDescription: "React Native application with geolocation services, push notifications, and beautiful weather animations. Integrates with multiple weather APIs for accurate forecasting.",
    image: "/project3-placeholder.jpg",
    technologies: ["React Native", "Expo", "TypeScript", "Weather API"],
    status: "Completed",
    date: "2023",
    githubUrl: "https://github.com/yourusername/weatherapp",
    liveUrl: null,
    category: "Mobile Development"
  },
  {
    id: 4,
    title: "AI Content Generator",
    description: "An AI-powered content generation tool for blogs, social media, and marketing copy.",
    longDescription: "Currently developing an AI-powered platform that generates high-quality content using OpenAI's GPT models. Features include multiple content types, tone customization, and export options.",
    image: "/project4-placeholder.jpg",
    technologies: ["Next.js", "OpenAI API", "Prisma", "PostgreSQL", "Tailwind CSS"],
    status: "Ongoing",
    date: "2024",
    githubUrl: "https://github.com/yourusername/ai-content",
    liveUrl: null,
    category: "AI/ML"
  },
  {
    id: 5,
    title: "Portfolio Website",
    description: "A modern, responsive portfolio website with glassmorphism design and smooth animations.",
    longDescription: "This very website! Built with Next.js, Framer Motion, and Tailwind CSS. Features include responsive design, smooth animations, contact form, and optimized performance.",
    image: "/project5-placeholder.jpg",
    technologies: ["Next.js", "Framer Motion", "Tailwind CSS", "TypeScript"],
    status: "Ongoing",
    date: "2024",
    githubUrl: "https://github.com/yourusername/portfolio",
    liveUrl: "https://yourportfolio.com",
    category: "Web Development"
  }
];

const ProjectCard = ({ project, index }: { project: any; index: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="glass-card group cursor-pointer"
      onClick={() => setIsExpanded(!isExpanded)}
    >
      {/* Project Image */}
      <div className="relative h-48 rounded-lg overflow-hidden mb-4">
        <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <span className="text-white text-lg font-medium">{project.title}</span>
        </div>
        <div className="absolute top-3 right-3">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            project.status === 'Completed' 
              ? 'bg-green-500/20 text-green-300' 
              : 'bg-yellow-500/20 text-yellow-300'
          }`}>
            {project.status}
          </span>
        </div>
      </div>

      {/* Project Info */}
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <h3 className="text-xl font-bold text-white group-hover:text-blue-300 transition-colors">
            {project.title}
          </h3>
          <div className="flex items-center space-x-1 text-white/60 text-sm">
            <Calendar size={14} />
            <span>{project.date}</span>
          </div>
        </div>

        <p className="text-white/80 text-sm leading-relaxed">
          {isExpanded ? project.longDescription : project.description}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2">
          {project.technologies.map((tech: string, techIndex: number) => (
            <span
              key={techIndex}
              className="px-2 py-1 bg-white/10 rounded-md text-xs text-white/80"
            >
              {tech}
            </span>
          ))}
        </div>

        {/* Links */}
        <div className="flex space-x-3 pt-2">
          {project.githubUrl && (
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 glass-nav px-4 py-2 text-white hover:bg-white/20 transition-all duration-300"
              onClick={(e) => e.stopPropagation()}
            >
              <Github size={16} />
              <span className="text-sm">Code</span>
            </a>
          )}
          {project.liveUrl && (
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 glass-nav px-4 py-2 text-white hover:bg-white/20 transition-all duration-300"
              onClick={(e) => e.stopPropagation()}
            >
              <ExternalLink size={16} />
              <span className="text-sm">Live</span>
            </a>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default function Projects() {
  const [filter, setFilter] = useState("All");
  const categories = ["All", "Web Development", "Mobile Development", "AI/ML"];
  
  const filteredProjects = filter === "All" 
    ? projects 
    : projects.filter(project => project.category === filter);

  return (
    <div className="min-h-screen py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold text-white mb-6">
            My <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Projects</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            A showcase of my work, from web applications to mobile apps and everything in between.
          </p>
        </motion.div>

        {/* Filter Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center mb-12"
        >
          <div className="glass-nav p-1 rounded-full">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setFilter(category)}
                className={`px-6 py-2 rounded-full transition-all duration-300 ${
                  filter === category
                    ? "bg-white/20 text-white"
                    : "text-white/60 hover:text-white hover:bg-white/10"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={filter}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredProjects.map((project, index) => (
              <ProjectCard key={project.id} project={project} index={index} />
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-16 glass-card text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-4">Interested in Working Together?</h2>
          <p className="text-white/80 mb-6 max-w-2xl mx-auto">
            I'm always open to discussing new opportunities and exciting projects. 
            Let's create something amazing together!
          </p>
          <a
            href="/contact"
            className="inline-flex items-center space-x-2 glass-nav px-8 py-3 text-white font-medium hover:bg-white/20 transition-all duration-300"
          >
            <span>Get In Touch</span>
            <ExternalLink size={18} />
          </a>
        </motion.div>
      </div>
    </div>
  );
}
