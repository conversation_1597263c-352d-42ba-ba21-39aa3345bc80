{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Image from \"next/image\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Download, Gith<PERSON>, Linkedin, Twitter, X } from \"lucide-react\";\n\nexport default function Home() {\n  const [showModal, setShowModal] = useState(false);\n\n  const toggleModal = () => setShowModal(!showModal);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center px-4 py-20\">\n      <div className=\"container mx-auto max-w-6xl\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Profile Image Section */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"flex justify-center lg:justify-end\"\n          >\n            <div className=\"relative group\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\n                className=\"relative cursor-pointer\"\n                onClick={toggleModal}\n              >\n                <div className=\"w-80 h-80 rounded-full overflow-hidden glass-card p-2\">\n                  <div className=\"w-full h-full rounded-full overflow-hidden relative\">\n                    <Image\n                      src=\"/profile-placeholder.jpg\"\n                      alt=\"Profile\"\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                      priority\n                      onError={(e) => {\n                        // Fallback to a placeholder if image doesn't exist\n                        const target = e.target as HTMLImageElement;\n                        target.src = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjMyMCIgdmlld0JveD0iMCAwIDMyMCAzMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMzIwIiBmaWxsPSIjNjY3RUVBIi8+CjxjaXJjbGUgY3g9IjE2MCIgY3k9IjEyMCIgcj0iNDAiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuOCIvPgo8cGF0aCBkPSJNMTAwIDI2MEMxMDAgMjIwIDEyNSAxOTAgMTYwIDE5MFMyMjAgMjIwIDIyMCAyNjBIMTAwWiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC44Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iMjkwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIj5Qcm9maWxlIEltYWdlPC90ZXh0Pgo8L3N2Zz4K\";\n                      }}\n                    />\n                  </div>\n                </div>\n\n                {/* Floating elements */}\n                <motion.div\n                  animate={{ y: [0, -10, 0] }}\n                  transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n                  className=\"absolute -top-4 -right-4 glass-nav p-3\"\n                >\n                  <span className=\"text-2xl\">👋</span>\n                </motion.div>\n\n                <motion.div\n                  animate={{ y: [0, 10, 0] }}\n                  transition={{ duration: 2.5, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 }}\n                  className=\"absolute -bottom-4 -left-4 glass-nav p-3\"\n                >\n                  <span className=\"text-2xl\">💻</span>\n                </motion.div>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Intro Section */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"space-y-6\"\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                className=\"text-5xl lg:text-6xl font-bold text-white\"\n              >\n                Hi, I'm{\" \"}\n                <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                  Your Name\n                </span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.6 }}\n                className=\"text-xl text-white/80 leading-relaxed\"\n              >\n                A passionate{\" \"}\n                <span className=\"text-blue-300 font-semibold\">Full-Stack Developer</span>{\" \"}\n                crafting beautiful, functional, and user-centered digital experiences.\n                I love turning complex problems into simple, elegant solutions.\n              </motion.p>\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              className=\"flex flex-wrap gap-4\"\n            >\n              <button className=\"glass-nav px-6 py-3 text-white font-medium hover:bg-white/20 transition-all duration-300 flex items-center space-x-2\">\n                <Download size={18} />\n                <span>Download CV</span>\n              </button>\n\n              <div className=\"flex space-x-3\">\n                <a\n                  href=\"https://github.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300\"\n                >\n                  <Github size={20} />\n                </a>\n                <a\n                  href=\"https://linkedin.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300\"\n                >\n                  <Linkedin size={20} />\n                </a>\n                <a\n                  href=\"https://twitter.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300\"\n                >\n                  <Twitter size={20} />\n                </a>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Profile Summary Modal */}\n      <AnimatePresence>\n        {showModal && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n            onClick={toggleModal}\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n              className=\"glass-card max-w-md w-full p-6 relative\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <button\n                onClick={toggleModal}\n                className=\"absolute top-4 right-4 text-white/60 hover:text-white transition-colors\"\n              >\n                <X size={24} />\n              </button>\n\n              <div className=\"space-y-4\">\n                <h3 className=\"text-2xl font-bold text-white\">Quick Summary</h3>\n                <div className=\"space-y-3 text-white/80\">\n                  <p>🎯 <strong>Focus:</strong> Full-Stack Web Development</p>\n                  <p>💼 <strong>Experience:</strong> 3+ Years</p>\n                  <p>🛠️ <strong>Specialties:</strong> React, Next.js, Node.js, TypeScript</p>\n                  <p>🌟 <strong>Passion:</strong> Creating seamless user experiences</p>\n                  <p>📍 <strong>Location:</strong> Your City, Country</p>\n                </div>\n                <div className=\"pt-4 border-t border-white/20\">\n                  <p className=\"text-sm text-white/60 italic\">\n                    \"Code is like humor. When you have to explain it, it's bad.\" - Cory House\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAAc,IAAM,aAAa,CAAC;IAExC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;oCAC1D,WAAU;oCACV,SAAS;;sDAET,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,QAAQ;oDACR,SAAS,CAAC;wDACR,mDAAmD;wDACnD,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,GAAG,GAAG;oDACf;;;;;;;;;;;;;;;;sDAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;4CAAC;4CAC1B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAG7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAG;oDAAI;iDAAE;4CAAC;4CACzB,YAAY;gDAAE,UAAU;gDAAK,QAAQ;gDAAU,MAAM;gDAAa,OAAO;4CAAI;4CAC7E,WAAU;sDAEV,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;gDACX;gDACS;8DACR,6LAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAK/F,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;gDACX;gDACc;8DACb,6LAAC;oDAAK,WAAU;8DAA8B;;;;;;gDAA4B;gDAAI;;;;;;;;;;;;;8CAMlF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;8DAEhB,6LAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;8DAElB,6LAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;8BAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,MAAM;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAC/B,YAAY;4BAAE,MAAM;4BAAU,WAAW;4BAAK,SAAS;wBAAG;wBAC1D,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;0CAGX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAE;kEAAG,6LAAC;kEAAO;;;;;;oDAAe;;;;;;;0DAC7B,6LAAC;;oDAAE;kEAAG,6LAAC;kEAAO;;;;;;oDAAoB;;;;;;;0DAClC,6LAAC;;oDAAE;kEAAI,6LAAC;kEAAO;;;;;;oDAAqB;;;;;;;0DACpC,6LAAC;;oDAAE;kEAAG,6LAAC;kEAAO;;;;;;oDAAiB;;;;;;;0DAC/B,6LAAC;;oDAAE;kEAAG,6LAAC;kEAAO;;;;;;oDAAkB;;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D;GArLwB;KAAA", "debugId": null}}]}