"use client";

import { useState } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Download, Gith<PERSON>, Linkedin, Twitter, X } from "lucide-react";

export default function Home() {
  const [showModal, setShowModal] = useState(false);

  const toggleModal = () => setShowModal(!showModal);

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-20">
      <div className="container mx-auto max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Profile Image Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="flex justify-center lg:justify-end"
          >
            <div className="relative group">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="relative cursor-pointer"
                onClick={toggleModal}
              >
                <div className="w-80 h-80 rounded-full overflow-hidden glass-card p-2">
                  <div className="w-full h-full rounded-full overflow-hidden relative">
                    <Image
                      src="/profile-placeholder.jpg"
                      alt="Profile"
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                      priority
                      onError={(e) => {
                        // Fallback to a placeholder if image doesn't exist
                        const target = e.target as HTMLImageElement;
                        target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjMyMCIgdmlld0JveD0iMCAwIDMyMCAzMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMzIwIiBmaWxsPSIjNjY3RUVBIi8+CjxjaXJjbGUgY3g9IjE2MCIgY3k9IjEyMCIgcj0iNDAiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuOCIvPgo8cGF0aCBkPSJNMTAwIDI2MEMxMDAgMjIwIDEyNSAxOTAgMTYwIDE5MFMyMjAgMjIwIDIyMCAyNjBIMTAwWiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC44Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iMjkwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIj5Qcm9maWxlIEltYWdlPC90ZXh0Pgo8L3N2Zz4K";
                      }}
                    />
                  </div>
                </div>

                {/* Floating elements */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-4 -right-4 glass-nav p-3"
                >
                  <span className="text-2xl">👋</span>
                </motion.div>

                <motion.div
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                  className="absolute -bottom-4 -left-4 glass-nav p-3"
                >
                  <span className="text-2xl">💻</span>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Intro Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-5xl lg:text-6xl font-bold text-white"
              >
                Hi, I'm{" "}
                <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Your Name
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="text-xl text-white/80 leading-relaxed"
              >
                A passionate{" "}
                <span className="text-blue-300 font-semibold">Full-Stack Developer</span>{" "}
                crafting beautiful, functional, and user-centered digital experiences.
                I love turning complex problems into simple, elegant solutions.
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="flex flex-wrap gap-4"
            >
              <button className="glass-nav px-6 py-3 text-white font-medium hover:bg-white/20 transition-all duration-300 flex items-center space-x-2">
                <Download size={18} />
                <span>Download CV</span>
              </button>

              <div className="flex space-x-3">
                <a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <Github size={20} />
                </a>
                <a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <Linkedin size={20} />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="glass-nav p-3 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <Twitter size={20} />
                </a>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Profile Summary Modal */}
      <AnimatePresence>
        {showModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
            onClick={toggleModal}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="glass-card max-w-md w-full p-6 relative"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={toggleModal}
                className="absolute top-4 right-4 text-white/60 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>

              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-white">Quick Summary</h3>
                <div className="space-y-3 text-white/80">
                  <p>🎯 <strong>Focus:</strong> Full-Stack Web Development</p>
                  <p>💼 <strong>Experience:</strong> 3+ Years</p>
                  <p>🛠️ <strong>Specialties:</strong> React, Next.js, Node.js, TypeScript</p>
                  <p>🌟 <strong>Passion:</strong> Creating seamless user experiences</p>
                  <p>📍 <strong>Location:</strong> Your City, Country</p>
                </div>
                <div className="pt-4 border-t border-white/20">
                  <p className="text-sm text-white/60 italic">
                    "Code is like humor. When you have to explain it, it's bad." - Cory House
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
