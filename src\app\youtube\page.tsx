"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Play, ExternalLink, Calendar, Eye, ThumbsUp, Users } from "lucide-react";

// Mock data - replace with actual YouTube API integration
const mockVideos = [
  {
    id: "1",
    title: "Building a Modern Portfolio with Next.js and Tailwind CSS",
    description: "Learn how to create a stunning portfolio website using Next.js, Tailwind CSS, and Framer Motion for smooth animations.",
    thumbnail: "/video1-placeholder.jpg",
    publishedAt: "2024-01-15",
    viewCount: "12,543",
    likeCount: "892",
    duration: "15:32",
    url: "https://youtube.com/watch?v=example1"
  },
  {
    id: "2",
    title: "React Hooks Explained: useState, useEffect, and Custom Hooks",
    description: "A comprehensive guide to React Hooks with practical examples and best practices for modern React development.",
    thumbnail: "/video2-placeholder.jpg",
    publishedAt: "2024-01-08",
    viewCount: "8,721",
    likeCount: "654",
    duration: "22:18",
    url: "https://youtube.com/watch?v=example2"
  },
  {
    id: "3",
    title: "TypeScript for Beginners: Complete Tutorial",
    description: "Everything you need to know about TypeScript, from basic types to advanced features like generics and decorators.",
    thumbnail: "/video3-placeholder.jpg",
    publishedAt: "2024-01-01",
    viewCount: "15,892",
    likeCount: "1,234",
    duration: "28:45",
    url: "https://youtube.com/watch?v=example3"
  },
  {
    id: "4",
    title: "Building RESTful APIs with Node.js and Express",
    description: "Learn how to create robust and scalable APIs using Node.js, Express, and MongoDB with authentication and validation.",
    thumbnail: "/video4-placeholder.jpg",
    publishedAt: "2023-12-25",
    viewCount: "9,876",
    likeCount: "743",
    duration: "31:12",
    url: "https://youtube.com/watch?v=example4"
  },
  {
    id: "5",
    title: "CSS Grid vs Flexbox: When to Use Which?",
    description: "A detailed comparison of CSS Grid and Flexbox with practical examples and use cases for modern web layouts.",
    thumbnail: "/video5-placeholder.jpg",
    publishedAt: "2023-12-18",
    viewCount: "6,543",
    likeCount: "521",
    duration: "18:27",
    url: "https://youtube.com/watch?v=example5"
  },
  {
    id: "6",
    title: "Deploying React Apps to Vercel: Complete Guide",
    description: "Step-by-step tutorial on deploying React applications to Vercel with custom domains and environment variables.",
    thumbnail: "/video6-placeholder.jpg",
    publishedAt: "2023-12-11",
    viewCount: "4,321",
    likeCount: "387",
    duration: "12:54",
    url: "https://youtube.com/watch?v=example6"
  }
];

const channelStats = {
  subscribers: "25.4K",
  totalViews: "1.2M",
  totalVideos: "47"
};

const VideoCard = ({ video, index }: { video: any; index: number }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="glass-card group cursor-pointer hover:scale-105 transition-all duration-300"
    >
      {/* Video Thumbnail */}
      <div className="relative h-48 rounded-lg overflow-hidden mb-4">
        <div className="w-full h-full bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center relative">
          <Play size={48} className="text-white opacity-80 group-hover:opacity-100 transition-opacity" />
          <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
            {video.duration}
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="space-y-3">
        <h3 className="text-lg font-bold text-white group-hover:text-blue-300 transition-colors line-clamp-2">
          {video.title}
        </h3>

        <p className="text-white/70 text-sm line-clamp-2 leading-relaxed">
          {video.description}
        </p>

        {/* Video Stats */}
        <div className="flex items-center justify-between text-white/60 text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Eye size={14} />
              <span>{video.viewCount}</span>
            </div>
            <div className="flex items-center space-x-1">
              <ThumbsUp size={14} />
              <span>{video.likeCount}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar size={14} />
            <span>{formatDate(video.publishedAt)}</span>
          </div>
        </div>

        {/* Watch Button */}
        <a
          href={video.url}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center space-x-2 glass-nav w-full py-3 text-white hover:bg-white/20 transition-all duration-300"
        >
          <Play size={16} />
          <span>Watch on YouTube</span>
          <ExternalLink size={16} />
        </a>
      </div>
    </motion.div>
  );
};

export default function YouTube() {
  const [videos, setVideos] = useState(mockVideos);
  const [loading, setLoading] = useState(false);

  // In a real implementation, you would fetch videos from YouTube API here
  useEffect(() => {
    // fetchYouTubeVideos();
  }, []);

  return (
    <div className="min-h-screen py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold text-white mb-6">
            My <span className="bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">YouTube</span> Channel
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Sharing knowledge through tutorials, tips, and insights about web development and programming.
          </p>
        </motion.div>

        {/* Channel Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-3 gap-6 mb-16"
        >
          <div className="glass-card text-center">
            <Users size={32} className="text-red-400 mx-auto mb-3" />
            <div className="text-3xl font-bold text-white mb-1">{channelStats.subscribers}</div>
            <div className="text-white/60">Subscribers</div>
          </div>
          <div className="glass-card text-center">
            <Eye size={32} className="text-blue-400 mx-auto mb-3" />
            <div className="text-3xl font-bold text-white mb-1">{channelStats.totalViews}</div>
            <div className="text-white/60">Total Views</div>
          </div>
          <div className="glass-card text-center">
            <Play size={32} className="text-green-400 mx-auto mb-3" />
            <div className="text-3xl font-bold text-white mb-1">{channelStats.totalVideos}</div>
            <div className="text-white/60">Videos</div>
          </div>
        </motion.div>

        {/* Latest Videos */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Latest Videos</h2>
          
          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="glass-card">
                  <div className="h-48 bg-white/10 rounded-lg mb-4 shimmer"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-white/10 rounded shimmer"></div>
                    <div className="h-3 bg-white/10 rounded shimmer"></div>
                    <div className="h-3 bg-white/10 rounded shimmer w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {videos.map((video, index) => (
                <VideoCard key={video.id} video={video} index={index} />
              ))}
            </div>
          )}
        </motion.div>

        {/* Subscribe CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="glass-card text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-4">Subscribe for More Content!</h2>
          <p className="text-white/80 mb-6 max-w-2xl mx-auto">
            Join our growing community of developers! Get notified when I upload new tutorials, 
            tips, and insights about web development, programming, and technology.
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="https://youtube.com/@yourchannel"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 bg-red-600 hover:bg-red-700 px-8 py-3 rounded-full text-white font-medium transition-all duration-300"
            >
              <Play size={18} />
              <span>Subscribe</span>
            </a>
            <a
              href="https://youtube.com/@yourchannel"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 glass-nav px-8 py-3 text-white font-medium hover:bg-white/20 transition-all duration-300"
            >
              <span>View Channel</span>
              <ExternalLink size={18} />
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
