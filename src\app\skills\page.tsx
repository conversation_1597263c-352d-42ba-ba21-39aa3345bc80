"use client";

import { motion } from "framer-motion";
import { Code, Database, Globe, Smartphone, Cloud, Wrench } from "lucide-react";

const skillCategories = [
  {
    title: "Frontend Development",
    icon: Globe,
    color: "from-blue-400 to-cyan-400",
    skills: [
      { name: "React", level: 95, description: "Building dynamic user interfaces" },
      { name: "Next.js", level: 90, description: "Full-stack React framework" },
      { name: "TypeScript", level: 88, description: "Type-safe JavaScript development" },
      { name: "Tailwind CSS", level: 92, description: "Utility-first CSS framework" },
      { name: "Framer Motion", level: 85, description: "Animation and interaction library" },
    ],
  },
  {
    title: "Backend Development",
    icon: Database,
    color: "from-green-400 to-emerald-400",
    skills: [
      { name: "Node.js", level: 90, description: "Server-side JavaScript runtime" },
      { name: "Express.js", level: 88, description: "Web application framework" },
      { name: "PostgreSQL", level: 85, description: "Relational database management" },
      { name: "MongoDB", level: 82, description: "NoSQL document database" },
      { name: "GraphQL", level: 78, description: "Query language for APIs" },
    ],
  },
  {
    title: "Mobile Development",
    icon: Smartphone,
    color: "from-purple-400 to-pink-400",
    skills: [
      { name: "React Native", level: 85, description: "Cross-platform mobile apps" },
      { name: "Expo", level: 80, description: "React Native development platform" },
      { name: "Flutter", level: 75, description: "Google's UI toolkit" },
    ],
  },
  {
    title: "Cloud & DevOps",
    icon: Cloud,
    color: "from-orange-400 to-red-400",
    skills: [
      { name: "AWS", level: 82, description: "Amazon Web Services" },
      { name: "Docker", level: 85, description: "Containerization platform" },
      { name: "Vercel", level: 90, description: "Deployment and hosting" },
      { name: "GitHub Actions", level: 80, description: "CI/CD automation" },
    ],
  },
  {
    title: "Programming Languages",
    icon: Code,
    color: "from-indigo-400 to-purple-400",
    skills: [
      { name: "JavaScript", level: 95, description: "Dynamic programming language" },
      { name: "TypeScript", level: 90, description: "Typed superset of JavaScript" },
      { name: "Python", level: 80, description: "General-purpose programming" },
      { name: "Java", level: 75, description: "Object-oriented programming" },
    ],
  },
  {
    title: "Tools & Others",
    icon: Wrench,
    color: "from-teal-400 to-blue-400",
    skills: [
      { name: "Git", level: 92, description: "Version control system" },
      { name: "VS Code", level: 95, description: "Code editor and IDE" },
      { name: "Figma", level: 85, description: "Design and prototyping tool" },
      { name: "Postman", level: 88, description: "API development and testing" },
    ],
  },
];

const SkillBar = ({ skill, index }: { skill: any; index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="space-y-2"
    >
      <div className="flex justify-between items-center">
        <span className="text-white font-medium">{skill.name}</span>
        <span className="text-white/60 text-sm">{skill.level}%</span>
      </div>
      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${skill.level}%` }}
          transition={{ duration: 1, delay: index * 0.1 + 0.5, ease: "easeOut" }}
          className="h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
        />
      </div>
      <p className="text-white/60 text-sm">{skill.description}</p>
    </motion.div>
  );
};

export default function Skills() {
  return (
    <div className="min-h-screen py-20 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-bold text-white mb-6">
            My <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Skills</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            A comprehensive overview of my technical expertise and the tools I use to bring ideas to life.
          </p>
        </motion.div>

        {/* Skills Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {skillCategories.map((category, categoryIndex) => {
            const Icon = category.icon;
            return (
              <motion.div
                key={categoryIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                className="glass-card"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${category.color}`}>
                    <Icon size={24} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-white">{category.title}</h2>
                </div>
                
                <div className="space-y-6">
                  {category.skills.map((skill, skillIndex) => (
                    <SkillBar key={skillIndex} skill={skill} index={skillIndex} />
                  ))}
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-16 glass-card text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-6">Always Learning</h2>
          <p className="text-white/80 leading-relaxed max-w-3xl mx-auto">
            Technology evolves rapidly, and I'm committed to staying current with the latest trends and best practices. 
            I regularly explore new frameworks, attend conferences, and contribute to open-source projects to expand 
            my knowledge and give back to the developer community.
          </p>
          <div className="mt-8 flex justify-center space-x-4">
            <div className="glass-nav px-6 py-3">
              <span className="text-blue-300 font-bold text-lg">50+</span>
              <p className="text-white/60 text-sm">Projects Completed</p>
            </div>
            <div className="glass-nav px-6 py-3">
              <span className="text-purple-300 font-bold text-lg">3+</span>
              <p className="text-white/60 text-sm">Years Experience</p>
            </div>
            <div className="glass-nav px-6 py-3">
              <span className="text-green-300 font-bold text-lg">20+</span>
              <p className="text-white/60 text-sm">Technologies Mastered</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
